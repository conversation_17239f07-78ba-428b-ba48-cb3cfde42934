// Serveur frontend simple pour la page de login
const http = require('http');
const url = require('url');
const PORT = 3000;

console.log('🚀 Démarrage du serveur frontend...');

// Page de connexion HTML
const loginPage = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MKA LMS - Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .login-container { min-height: 100vh; display: flex; align-items: center; }
        .login-card { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); border: none; border-radius: 1rem; }
        .login-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 1rem 1rem 0 0; }
    </style>
</head>
<body>
    <div class="container-fluid login-container">
        <div class="row w-100 justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card login-card">
                    <div class="card-header login-header text-center py-4">
                        <h2 class="mb-0">🎓 MKA LMS</h2>
                        <p class="mb-0">Plateforme d'apprentissage</p>
                    </div>
                    <div class="card-body p-5">
                        <div id="error-message" class="alert alert-danger d-none" role="alert"></div>
                        
                        <form id="loginForm">
                            <div class="form-floating mb-4">
                                <input type="email" class="form-control" id="email" placeholder="Email" value="<EMAIL>" required>
                                <label for="email">Adresse email</label>
                                <div class="invalid-feedback" id="email-error"></div>
                            </div>

                            <div class="form-floating mb-4">
                                <input type="password" class="form-control" id="password" placeholder="Mot de passe" value="123456" required>
                                <label for="password">Mot de passe</label>
                                <div class="invalid-feedback" id="password-error"></div>
                            </div>

                            <div class="d-flex justify-content-between mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">Se souvenir de moi</label>
                                </div>
                                <a href="#" class="text-decoration-none">Mot de passe oublié ?</a>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 py-3 mb-3" id="loginBtn">
                                <span id="loginText">Se connecter</span>
                                <span id="loginSpinner" class="spinner-border spinner-border-sm d-none ms-2"></span>
                            </button>
                        </form>

                        <div class="alert alert-info mt-4">
                            <h6><i class="bi bi-info-circle"></i> Identifiants de test :</h6>
                            <p class="mb-1"><strong>Email :</strong> <EMAIL></p>
                            <p class="mb-0"><strong>Mot de passe :</strong> 123456</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Pré-remplir l'email depuis l'URL si fourni
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const emailParam = urlParams.get('email');
            if (emailParam) {
                document.getElementById('email').value = emailParam;
                console.log('📧 Email pré-rempli depuis l\\'URL:', emailParam);
            }
        };

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.classList.remove('d-none');
        }

        function hideError() {
            const errorDiv = document.getElementById('error-message');
            errorDiv.classList.add('d-none');
        }

        function validate() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            let valid = true;

            document.getElementById('email').classList.remove('is-invalid');
            document.getElementById('password').classList.remove('is-invalid');
            hideError();

            if (!email) {
                document.getElementById('email').classList.add('is-invalid');
                document.getElementById('email-error').textContent = 'Email requis';
                valid = false;
            } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {
                document.getElementById('email').classList.add('is-invalid');
                document.getElementById('email-error').textContent = 'Email invalide';
                valid = false;
            }

            if (!password) {
                document.getElementById('password').classList.add('is-invalid');
                document.getElementById('password-error').textContent = 'Mot de passe requis';
                valid = false;
            } else if (password.length < 6) {
                document.getElementById('password').classList.add('is-invalid');
                document.getElementById('password-error').textContent = 'Le mot de passe doit contenir au moins 6 caractères';
                valid = false;
            }

            return valid;
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!validate()) return;

            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');
            
            loginBtn.disabled = true;
            loginText.textContent = 'Connexion...';
            loginSpinner.classList.remove('d-none');
            hideError();

            const formData = {
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                rememberMe: document.getElementById('rememberMe').checked
            };

            console.log('=== LOGIN REQUEST ===');
            console.log('Email:', formData.email);
            console.log('Remember Me:', formData.rememberMe ? 'CHECKED' : 'NOT CHECKED');

            try {
                const response = await fetch('http://localhost:8000/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                console.log('Login response:', data);

                if (data.success) {
                    const userData = {
                        id: data.data.id,
                        email: formData.email,
                        role: data.data.role || 'Etudiant',
                        name: data.data.name || formData.email.split('@')[0],
                        profilePic: data.data.profilePic || null,
                        token: data.data.access_token || 'temp_token_' + Date.now(),
                        rememberMe: formData.rememberMe
                    };

                    console.log('Created userData object:', userData);

                    if (formData.rememberMe) {
                        localStorage.setItem('user', JSON.stringify(userData));
                        console.log('✅ User stored in localStorage (persistent login)');
                    } else {
                        sessionStorage.setItem('user', JSON.stringify(userData));
                        console.log('✅ User stored in sessionStorage (session only)');
                    }

                    alert('✅ Connexion réussie ! Bienvenue ' + userData.name);
                    window.location.href = '/dashboard';
                } else {
                    showError(data.message || 'Connexion échouée');
                }
            } catch (error) {
                console.error('Login error:', error);
                showError('Erreur de connexion. Vérifiez que le serveur backend fonctionne.');
            }

            loginBtn.disabled = false;
            loginText.textContent = 'Se connecter';
            loginSpinner.classList.add('d-none');
        });
    </script>
</body>
</html>`;

// Page de tableau de bord
const dashboardPage = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MKA LMS - Tableau de bord</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white text-center">
                        <h2><i class="bi bi-check-circle"></i> Connexion réussie !</h2>
                    </div>
                    <div class="card-body p-5">
                        <div class="alert alert-success text-center">
                            <h4>🎉 Bienvenue sur MKA LMS !</h4>
                            <p>Le système de connexion fonctionne parfaitement.</p>
                            <p><strong>Le lien email fonctionne correctement !</strong></p>
                        </div>
                        
                        <div id="user-info" class="alert alert-info">
                            <h5><i class="bi bi-person-circle"></i> Informations utilisateur :</h5>
                            <div id="user-details">Chargement...</div>
                        </div>
                        
                        <div class="text-center">
                            <button onclick="logout()" class="btn btn-danger me-3">
                                <i class="bi bi-box-arrow-right"></i> Se déconnecter
                            </button>
                            <a href="/login" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Retour à la connexion
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        window.onload = function() {
            const user = JSON.parse(localStorage.getItem('user') || sessionStorage.getItem('user') || '{}');
            
            if (user.email) {
                document.getElementById('user-details').innerHTML = 
                    '<p><strong>Email:</strong> ' + user.email + '</p>' +
                    '<p><strong>Nom:</strong> ' + (user.name || 'Non défini') + '</p>' +
                    '<p><strong>Rôle:</strong> ' + user.role + '</p>' +
                    '<p><strong>Remember Me:</strong> ' + (user.rememberMe ? 'Oui' : 'Non') + '</p>' +
                    '<p><strong>Token:</strong> <code>' + (user.token ? user.token.substring(0, 30) + '...' : 'Non défini') + '</code></p>';
            } else {
                document.getElementById('user-details').innerHTML = 'Aucune information utilisateur trouvée';
            }
        };
        
        function logout() {
            localStorage.removeItem('user');
            sessionStorage.removeItem('user');
            alert('Déconnexion réussie !');
            window.location.href = '/login';
        }
    </script>
</body>
</html>`;

// Créer le serveur
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  console.log('📡 ' + req.method + ' ' + pathname);

  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (pathname === '/' || pathname === '/login') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(loginPage);
    return;
  }

  if (pathname === '/dashboard') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(dashboardPage);
    return;
  }

  // 404
  res.writeHead(404, { 'Content-Type': 'text/html' });
  res.end('<html><head><title>404</title></head><body><h1>404 - Page non trouvée</h1><p><a href="/login">Retour à la connexion</a></p></body></html>');
});

// Démarrage du serveur
server.listen(PORT, () => {
  console.log('🎉 ========================================');
  console.log('🎉 SERVEUR FRONTEND DÉMARRÉ AVEC SUCCÈS !');
  console.log('🎉 ========================================');
  console.log('🌐 URL: http://localhost:' + PORT);
  console.log('🔗 Page de connexion: http://localhost:' + PORT + '/login');
  console.log('📱 Compatible avec le lien email !');
  console.log('🎉 ========================================');
});

process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur frontend...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur frontend...');
  process.exit(0);
});
