// Serveur simple pour tester la fonctionnalité de connexion
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');

const app = express();
const PORT = 8000;

// Middleware
app.use(cors());
app.use(express.json());

// Données de test (normalement viendraient de la base de données)
const testUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: 123456
    role: 'Admin',
    name: '<PERSON><PERSON><PERSON>'
  }
];

// Routes de test
app.get('/', (req, res) => {
  res.json({ 
    message: 'Backend MKA-LMS is running!', 
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

app.get('/auth', (req, res) => {
  res.json({
    success: true,
    data: testUsers.map(user => ({
      id: user.id,
      email: user.email,
      role: user.role,
      name: user.name
    }))
  });
});

app.post('/auth/login', async (req, res) => {
  try {
    const { email, password, rememberMe } = req.body;
    
    console.log('Login request received:', { email, rememberMe });
    
    // Trouver l'utilisateur
    const user = testUsers.find(u => u.email === email);
    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email'
      });
    }
    
    // Vérifier le mot de passe
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: 'Invalid password'
      });
    }
    
    // Connexion réussie
    res.json({
      success: true,
      message: 'Connexion réussie',
      data: {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        rememberMe: rememberMe || false,
        access_token: `temp_token_${Date.now()}_${rememberMe ? 'long' : 'short'}`
      }
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur'
    });
  }
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur backend démarré sur http://localhost:${PORT}`);
  console.log(`📋 Utilisateur de test:`);
  console.log(`   Email: <EMAIL>`);
  console.log(`   Mot de passe: 123456`);
  console.log(`🌐 API Documentation: http://localhost:${PORT}/`);
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});
