"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const bcrypt = require("bcrypt");
const mail_service_1 = require("../mail/mail.service");
let UsersService = class UsersService {
    prisma;
    mailService;
    constructor(prisma, mailService) {
        this.prisma = prisma;
        this.mailService = mailService;
    }
    async hashPassword(password) {
        return bcrypt.hash(password, 10);
    }
    generateTempPassword(length = 10) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%';
        return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
    }
    async create(createUserDto) {
        const tempPassword = this.generateTempPassword();
        const hashedPassword = await this.hashPassword(tempPassword);
        const newUser = await this.prisma.user.create({
            data: {
                email: createUserDto.email,
                password: hashedPassword,
                role: createUserDto.role,
                name: createUserDto.name,
                phone: createUserDto.phone,
                location: createUserDto.location,
                about: createUserDto.about,
                skills: createUserDto.skills ? [createUserDto.skills] : undefined,
            },
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
        await this.mailService.sendWelcomeEmail(newUser.email, tempPassword, newUser.role);
        return newUser;
    }
    async findAll() {
        return this.prisma.user.findMany({
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
    }
    async findOne(id) {
        return this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                email: true,
                name: true,
                role: true,
                phone: true,
                location: true,
                about: true,
                skills: true,
                profilePic: true,
            },
        });
    }
    async update(id, updateUserDto) {
        return this.prisma.user.update({
            where: { id },
            data: {
                name: updateUserDto.name,
                role: updateUserDto.role,
                phone: updateUserDto.phone,
                location: updateUserDto.location,
                about: updateUserDto.about,
                skills: Array.isArray(updateUserDto.skills)
                    ? updateUserDto.skills
                    : (typeof updateUserDto.skills === 'string'
                        ? [updateUserDto.skills]
                        : undefined),
                profilePic: updateUserDto.profilePic,
            },
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                profilePic: true,
                location: true,
                skills: true,
                about: true,
            },
        });
    }
    async remove(id) {
        return this.prisma.user.delete({
            where: { id },
        });
    }
    async findById(id) {
        try {
            const numericId = parseInt(String(id), 10);
            if (isNaN(numericId))
                throw new Error('ID invalide');
            return this.prisma.user.findUnique({
                where: { id: numericId },
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    profilePic: true,
                    location: true,
                    skills: true,
                    about: true,
                },
            });
        }
        catch (error) {
            console.error('Erreur dans findById:', error);
            throw error;
        }
    }
    async findByEmail(email) {
        return this.prisma.user.findUnique({
            where: { email },
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                profilePic: true,
                location: true,
                skills: true,
                about: true,
            },
        });
    }
    async updateByEmail(email, updateUserDto) {
        try {
            console.log("Mise à jour de l'utilisateur avec email:", email);
            console.log("Données reçues:", updateUserDto);
            if (updateUserDto.skills) {
                console.log("Skills avant traitement:", updateUserDto.skills);
                console.log("Type de skills:", typeof updateUserDto.skills);
                if (typeof updateUserDto.skills === 'string') {
                    try {
                        if (updateUserDto.skills.startsWith('[') && updateUserDto.skills.endsWith(']')) {
                            updateUserDto.skills = JSON.parse(updateUserDto.skills);
                            console.log("Skills après parsing JSON:", updateUserDto.skills);
                        }
                        else {
                            updateUserDto.skills = [updateUserDto.skills];
                            console.log("Skills convertis en tableau:", updateUserDto.skills);
                        }
                    }
                    catch (e) {
                        console.error('Failed to parse skills:', e);
                        updateUserDto.skills = [];
                    }
                }
                else if (Array.isArray(updateUserDto.skills)) {
                    console.log("Skills est déjà un tableau:", updateUserDto.skills);
                }
                else {
                    console.error("Format de skills non reconnu, conversion en tableau vide");
                    updateUserDto.skills = [];
                }
            }
            const updateData = {
                name: updateUserDto.name,
                phone: updateUserDto.phone,
                location: updateUserDto.location,
                about: updateUserDto.about,
            };
            if (updateUserDto.skills !== undefined) {
                updateData.skills = updateUserDto.skills;
            }
            if (updateUserDto.profilePic !== undefined) {
                updateData.profilePic = updateUserDto.profilePic;
            }
            return await this.prisma.user.update({
                where: { email },
                data: updateData,
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    profilePic: true,
                    location: true,
                    skills: true,
                    about: true,
                },
            });
        }
        catch (error) {
            console.error("Erreur dans updateByEmail:", error);
            throw error;
        }
    }
    async updateProfilePic(id, profilePicPath) {
        try {
            return await this.prisma.user.update({
                where: { id },
                data: {
                    profilePic: profilePicPath,
                },
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    profilePic: true,
                    location: true,
                    skills: true,
                    about: true,
                },
            });
        }
        catch (error) {
            console.error("Erreur dans updateProfilePic:", error);
            throw error;
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService,
        mail_service_1.MailService])
], UsersService);
//# sourceMappingURL=users.service.js.map