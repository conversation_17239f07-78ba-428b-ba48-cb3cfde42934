import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🌱 Starting database seeding...');

    // Vérifier si l'utilisateur admin existe déjà
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('✅ Admin user already exists');
      return;
    }

    const salt = await bcrypt.genSalt();
    const hashedPassword = await bcrypt.hash('123456', salt);

    const admin = await prisma.user.create({
      data: {
        role: 'Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON><PERSON><PERSON> Admin',
      },
    });

    console.log('✅ Admin user created:', admin.email);
    console.log('🔑 Password: 123456');

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => {
    console.log('🎉 Database seeding completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Database seeding failed:', error);
    process.exit(1);
  });
