// Serveur backend simple pour la page de login
const http = require('http');
const url = require('url');
const PORT = 8000;

console.log('🚀 Démarrage du serveur backend...');

// Base de données simulée
const users = [
  {
    id: 1,
    email: '<EMAIL>',
    password: '123456',
    role: 'Admin',
    name: '<PERSON><PERSON><PERSON>',
    phone: null,
    profilePic: null,
    location: null,
    skills: [],
    about: null
  }
];

// Fonction pour parser le JSON du body
function parseBody(req) {
  return new Promise((resolve) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        resolve({});
      }
    });
  });
}

// Fonction pour envoyer une réponse JSON
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Créer le serveur
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(method + ' ' + path);

  // Gestion CORS pour les requêtes OPTIONS
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // Route de test
  if (path === '/' && method === 'GET') {
    sendJSON(res, { 
      message: 'Backend MKA-LMS fonctionne!', 
      timestamp: new Date().toISOString(),
      status: 'OK'
    });
    return;
  }

  // Route pour lister les utilisateurs
  if (path === '/auth' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: users.map(user => ({
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name
      }))
    });
    return;
  }

  // Route de connexion - Compatible avec votre LoginPage.js
  if (path === '/auth/login' && method === 'POST') {
    const body = await parseBody(req);
    const { email, password, rememberMe } = body;
    
    console.log('Login request received:', {
      email: email,
      rememberMe: rememberMe
    });
    
    if (!email || !password) {
      sendJSON(res, {
        success: false,
        message: 'Email et mot de passe requis'
      }, 400);
      return;
    }
    
    const user = users.find(u => u.email === email && u.password === password);
    
    if (user) {
      console.log('✅ Connexion réussie pour:', email);
      
      // Réponse compatible avec votre LoginPage.js
      sendJSON(res, {
        success: true,
        message: 'Connexion réussie',
        data: {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name,
          phone: user.phone,
          profilePic: user.profilePic,
          location: user.location,
          skills: user.skills,
          about: user.about,
          rememberMe: rememberMe || false,
          access_token: 'temp_token_' + Date.now() + '_' + (rememberMe ? 'long' : 'short')
        }
      });
    } else {
      console.log('❌ Connexion échouée pour:', email);
      sendJSON(res, {
        success: false,
        message: 'Email ou mot de passe incorrect'
      }, 401);
    }
    return;
  }

  // Route pour mot de passe oublié
  if (path === '/auth/forgot-password' && method === 'POST') {
    const body = await parseBody(req);
    const { email } = body;
    
    console.log('Forgot password request for:', email);
    
    sendJSON(res, {
      success: true,
      message: 'Email de réinitialisation envoyé'
    });
    return;
  }

  // 404
  sendJSON(res, {
    success: false,
    message: 'Route non trouvée'
  }, 404);
});

// Démarrage du serveur
server.listen(PORT, () => {
  console.log('🎉 ========================================');
  console.log('🎉 SERVEUR BACKEND DÉMARRÉ AVEC SUCCÈS !');
  console.log('🎉 ========================================');
  console.log('🌐 URL: http://localhost:' + PORT);
  console.log('🔗 API Login: http://localhost:' + PORT + '/auth/login');
  console.log('📋 Identifiants de test:');
  console.log('   📧 Email: <EMAIL>');
  console.log('   🔑 Mot de passe: 123456');
  console.log('🎉 ========================================');
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur backend...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur backend...');
  process.exit(0);
});
