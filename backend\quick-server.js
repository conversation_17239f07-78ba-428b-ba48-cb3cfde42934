// Serveur Express simple pour résoudre le problème 404
console.log('🚀 Démarrage du serveur backend...');

const express = require('express');
const app = express();
const PORT = 8000;

// Middleware de base
app.use(express.json());
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Route de base
app.get('/', (req, res) => {
  console.log('📡 Requête reçue sur /');
  res.json({ 
    message: 'Backend MKA-LMS fonctionne!', 
    timestamp: new Date().toISOString(),
    status: 'OK',
    port: PORT
  });
});

// Route auth pour lister les utilisateurs
app.get('/auth', (req, res) => {
  console.log('📡 Requête reçue sur /auth');
  res.json({
    success: true,
    data: [
      {
        id: 1,
        email: '<EMAIL>',
        role: 'Admin',
        name: 'Khalil Admin'
      }
    ]
  });
});

// Route de connexion
app.post('/auth/login', async (req, res) => {
  console.log('📡 Requête de connexion reçue:', req.body);
  
  const { email, password, rememberMe } = req.body;
  
  // Validation simple
  if (email === '<EMAIL>' && password === '123456') {
    console.log('✅ Connexion réussie pour:', email);
    res.json({
      success: true,
      message: 'Connexion réussie',
      data: {
        id: 1,
        email: email,
        role: 'Admin',
        name: 'Khalil Admin',
        rememberMe: rememberMe || false,
        access_token: `temp_token_${Date.now()}_${rememberMe ? 'long' : 'short'}`
      }
    });
  } else {
    console.log('❌ Connexion échouée pour:', email);
    res.status(400).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur serveur interne'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🎉 Serveur backend démarré avec succès!`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`📋 Identifiants de test:`);
  console.log(`   Email: <EMAIL>`);
  console.log(`   Mot de passe: 123456`);
  console.log(`🔗 Testez: http://localhost:${PORT}/`);
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});
