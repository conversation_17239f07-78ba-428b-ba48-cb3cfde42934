// Script simple pour démarrer le serveur NestJS
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Démarrage du serveur backend...');

// Utiliser ts-node pour démarrer le serveur en mode développement
const serverProcess = spawn('node', [
  path.join(__dirname, 'node_modules', 'ts-node', 'dist', 'bin.js'),
  path.join(__dirname, 'src', 'main.ts')
], {
  stdio: 'inherit',
  cwd: __dirname
});

serverProcess.on('error', (error) => {
  console.error('❌ Erreur lors du démarrage du serveur:', error);
});

serverProcess.on('exit', (code) => {
  console.log(`🔴 Serveur arrêté avec le code: ${code}`);
});

// Gérer l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur...');
  serverProcess.kill('SIGTERM');
  process.exit(0);
});
