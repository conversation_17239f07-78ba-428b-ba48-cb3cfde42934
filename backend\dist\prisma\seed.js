"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcrypt");
const prisma = new client_1.PrismaClient();
async function main() {
    try {
        console.log('🌱 Starting database seeding...');
        const existingAdmin = await prisma.user.findUnique({
            where: { email: '<EMAIL>' }
        });
        if (existingAdmin) {
            console.log('✅ Admin user already exists');
            return;
        }
        const salt = await bcrypt.genSalt();
        const hashedPassword = await bcrypt.hash('123456', salt);
        const admin = await prisma.user.create({
            data: {
                role: 'Admin',
                email: '<EMAIL>',
                password: hashedPassword,
                name: 'Khalil Admin',
            },
        });
        console.log('✅ Admin user created:', admin.email);
        console.log('🔑 Password: 123456');
    }
    catch (error) {
        console.error('❌ Error during seeding:', error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
main()
    .then(() => {
    console.log('🎉 Database seeding completed successfully');
    process.exit(0);
})
    .catch((error) => {
    console.error('💥 Database seeding failed:', error);
    process.exit(1);
});
//# sourceMappingURL=seed.js.map