// Serveur MKA-LMS tout-en-un - Backend + Frontend (Node.js natif)
const http = require('http');
const url = require('url');
const querystring = require('querystring');
const PORT = 3000;

console.log('🚀 Démarrage du serveur MKA-LMS...');

// Base de données simulée
const users = [
  {
    id: 1,
    email: '<EMAIL>',
    password: '123456', // En production, ce serait hashé
    role: 'Admin',
    name: '<PERSON><PERSON><PERSON>'
  }
];

// Fonction pour parser le JSON du body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        resolve({});
      }
    });
  });
}

// ==================== ROUTES API ====================

// Route de test
app.get('/api/test', (req, res) => {
  console.log('📡 Test API appelé');
  res.json({ 
    message: 'MKA-LMS API fonctionne!', 
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour lister les utilisateurs
app.get('/api/auth', (req, res) => {
  console.log('📡 Liste des utilisateurs demandée');
  res.json({
    success: true,
    data: users.map(user => ({
      id: user.id,
      email: user.email,
      role: user.role,
      name: user.name
    }))
  });
});

// Route de connexion
app.post('/api/auth/login', (req, res) => {
  console.log('📡 Tentative de connexion:', req.body);
  
  const { email, password, rememberMe } = req.body;
  
  // Validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email et mot de passe requis'
    });
  }
  
  // Recherche de l'utilisateur
  const user = users.find(u => u.email === email && u.password === password);
  
  if (user) {
    console.log('✅ Connexion réussie pour:', email);
    res.json({
      success: true,
      message: 'Connexion réussie',
      data: {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        rememberMe: rememberMe || false,
        access_token: `mka_token_${Date.now()}_${user.id}`
      }
    });
  } else {
    console.log('❌ Connexion échouée pour:', email);
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// ==================== ROUTES FRONTEND ====================

// Page de connexion
app.get('/login', (req, res) => {
  console.log('📱 Page de connexion demandée');
  res.send(`
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MKA LMS - Connexion</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); width: 100%; max-width: 400px; }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { color: #1976d2; font-size: 2.5em; margin-bottom: 10px; }
        .logo p { color: #666; font-size: 1.1em; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; color: #333; font-weight: 500; }
        input[type="email"], input[type="password"] { width: 100%; padding: 12px 15px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        input[type="email"]:focus, input[type="password"]:focus { outline: none; border-color: #1976d2; }
        .checkbox-group { display: flex; align-items: center; margin-bottom: 25px; }
        .checkbox-group input { margin-right: 10px; }
        .login-btn { width: 100%; background: #1976d2; color: white; padding: 12px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: background 0.3s; }
        .login-btn:hover { background: #1565c0; }
        .login-btn:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin-top: 20px; padding: 15px; border-radius: 8px; display: none; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .credentials { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px; border-left: 4px solid #1976d2; }
        .credentials h4 { color: #1976d2; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🎓 MKA LMS</h1>
            <p>Plateforme d'apprentissage</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Adresse email</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Mot de passe</label>
                <input type="password" id="password" value="123456" required>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="rememberMe">
                <label for="rememberMe">Se souvenir de moi</label>
            </div>
            
            <button type="submit" class="login-btn">Se connecter</button>
        </form>
        
        <div id="result" class="result"></div>
        
        <div class="credentials">
            <h4>📋 Identifiants de test</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Mot de passe:</strong> 123456</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const submitBtn = document.querySelector('.login-btn');
            
            // Afficher le loading
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '⏳ Connexion en cours...';
            submitBtn.disabled = true;
            submitBtn.textContent = 'Connexion...';
            
            const formData = {
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                rememberMe: document.getElementById('rememberMe').checked
            };
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = \`
                        ✅ <strong>Connexion réussie!</strong><br>
                        Bienvenue \${data.data.name}<br>
                        Rôle: \${data.data.role}<br>
                        <small>Redirection vers le tableau de bord...</small>
                    \`;
                    
                    // Simuler la redirection après 2 secondes
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                    
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ ' + data.message;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Erreur de connexion: ' + error.message;
            }
            
            // Restaurer le bouton
            submitBtn.disabled = false;
            submitBtn.textContent = 'Se connecter';
        });
    </script>
</body>
</html>
  `);
});

// Page d'accueil après connexion
app.get('/dashboard', (req, res) => {
  console.log('📱 Dashboard demandé');
  res.send(`
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MKA LMS - Tableau de bord</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #1976d2; margin-bottom: 30px; }
        .success-message { background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin-bottom: 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 MKA LMS - Tableau de bord</h1>
        </div>
        <div class="success-message">
            <h2>✅ Connexion réussie !</h2>
            <p>Bienvenue sur la plateforme MKA LMS. Le système de connexion fonctionne parfaitement.</p>
            <p><strong>Le lien email fonctionne correctement !</strong></p>
        </div>
        <p><a href="/login">← Retour à la page de connexion</a></p>
    </div>
</body>
</html>
  `);
});

// Page d'accueil
app.get('/', (req, res) => {
  console.log('📱 Page d\'accueil demandée');
  res.redirect('/login');
});

// Gestion des erreurs 404
app.use((req, res) => {
  console.log('❌ 404 - Page non trouvée:', req.path);
  res.status(404).send(`
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MKA LMS - Page non trouvée</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
        h1 { color: #1976d2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 MKA LMS</h1>
        <h2>❌ Page non trouvée (404)</h2>
        <p>La page demandée n'existe pas.</p>
        <p><a href="/login">🔗 Aller à la page de connexion</a></p>
    </div>
</body>
</html>
  `);
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log('🎉 ========================================');
  console.log('🎉 SERVEUR MKA-LMS DÉMARRÉ AVEC SUCCÈS !');
  console.log('🎉 ========================================');
  console.log(`🌐 URL principale: http://localhost:${PORT}`);
  console.log(`🔗 Page de connexion: http://localhost:${PORT}/login`);
  console.log(`📋 Identifiants de test:`);
  console.log(`   📧 Email: <EMAIL>`);
  console.log(`   🔑 Mot de passe: 123456`);
  console.log('🎉 ========================================');
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur MKA-LMS...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur MKA-LMS...');
  process.exit(0);
});
