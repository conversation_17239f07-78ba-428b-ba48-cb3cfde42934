<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Connexion - MKA LMS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #1976d2;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #1976d2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1565c0; }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .email-template {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test de Connexion - MKA LMS</h1>
            <p>Test de la fonctionnalité de connexion et du lien email</p>
        </div>

        <!-- Test 1: Vérification du serveur backend -->
        <div class="test-section info">
            <h3>1. Test de connectivité du serveur backend</h3>
            <p>Vérification que le serveur backend fonctionne sur http://localhost:8000</p>
            <button onclick="testBackendConnectivity()">Tester la connectivité</button>
            <div id="backend-result" class="result"></div>
        </div>

        <!-- Test 2: Simulation de l'email de bienvenue -->
        <div class="test-section info">
            <h3>2. Simulation de l'email de bienvenue</h3>
            <div class="email-template">
                <h4 style="color:#1976d2;text-align:center">Bienvenue sur la plateforme LMS!</h4>
                <p>Votre compte a été créé avec succès. Voici vos identifiants de connexion :</p>
                <div style="background:#f5f5f5;padding:15px;border-radius:5px;margin:20px 0">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Mot de passe temporaire:</strong> <span style="font-family:monospace;background:#e0e0e0;padding:3px 6px;border-radius:3px">123456</span></p>
                    <p><strong>Rôle:</strong> Admin</p>
                </div>
                <p style="color:#666;font-size:14px;margin:15px 0">
                    Cliquez sur le bouton ci-dessous pour vous connecter à la plateforme avec vos identifiants :
                </p>
                <div style="text-align:center;margin-top:30px">
                    <a href="http://localhost:3000/login" 
                       style="background:#1976d2;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;font-weight:bold"
                       target="_blank">
                        Se connecter à la plateforme
                    </a>
                </div>
            </div>
        </div>

        <!-- Test 3: Test de connexion -->
        <div class="test-section info">
            <h3>3. Test de connexion</h3>
            <form onsubmit="testLogin(event)">
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">Mot de passe:</label>
                    <input type="password" id="password" value="123456" required>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="rememberMe"> Se souvenir de moi
                    </label>
                </div>
                <button type="submit">Tester la connexion</button>
            </form>
            <div id="login-result" class="result"></div>
        </div>

        <!-- Instructions -->
        <div class="test-section info">
            <h3>📋 Instructions</h3>
            <ol>
                <li><strong>Démarrer le serveur backend:</strong> Assurez-vous que le serveur backend fonctionne sur le port 8000</li>
                <li><strong>Démarrer le serveur frontend:</strong> Assurez-vous que le serveur frontend fonctionne sur le port 3000</li>
                <li><strong>Tester la connectivité:</strong> Cliquez sur "Tester la connectivité" pour vérifier le backend</li>
                <li><strong>Tester le lien email:</strong> Cliquez sur "Se connecter à la plateforme" dans l'email simulé</li>
                <li><strong>Tester la connexion:</strong> Utilisez le formulaire de test avec les identifiants fournis</li>
            </ol>
        </div>
    </div>

    <script>
        async function testBackendConnectivity() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '⏳ Test en cours...';

            try {
                const response = await fetch('http://localhost:8000/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Serveur backend accessible<br>Réponse: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Serveur backend non accessible (Status: ${response.status})`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur de connexion: ${error.message}<br>Le serveur backend n'est probablement pas démarré sur le port 8000`;
            }
        }

        async function testLogin(event) {
            event.preventDefault();
            
            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '⏳ Test de connexion en cours...';

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            try {
                const response = await fetch('http://localhost:8000/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        rememberMe: rememberMe
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Connexion réussie!<br>
                        <strong>Utilisateur:</strong> ${data.data.email}<br>
                        <strong>Rôle:</strong> ${data.data.role}<br>
                        <strong>Token:</strong> ${data.data.access_token}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Connexion échouée: ${data.message || 'Erreur inconnue'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur de connexion: ${error.message}<br>Vérifiez que le serveur backend fonctionne`;
            }
        }
    </script>
</body>
</html>
