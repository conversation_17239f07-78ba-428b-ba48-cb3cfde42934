// Script de test simple pour vérifier la connectivité backend
const axios = require('axios');

async function testBackend() {
  console.log('🧪 Test de connectivité backend...\n');

  // Test 1: Vérifier si le serveur backend répond
  try {
    console.log('1. Test de connectivité serveur...');
    const response = await axios.get('http://localhost:8000', { timeout: 5000 });
    console.log('✅ Serveur backend accessible');
    console.log('   Réponse:', response.data);
  } catch (error) {
    console.log('❌ Serveur backend non accessible');
    console.log('   Erreur:', error.code || error.message);
    console.log('   Le serveur backend n\'est probablement pas démarré');
    return false;
  }

  // Test 2: Tester l'endpoint d'authentification
  try {
    console.log('\n2. Test endpoint auth...');
    const response = await axios.get('http://localhost:8000/auth', { timeout: 5000 });
    console.log('✅ Endpoint auth accessible');
    if (response.data.success && response.data.data) {
      console.log('   Utilisateurs trouvés:', response.data.data.length);
    }
  } catch (error) {
    console.log('❌ Endpoint auth non accessible');
    console.log('   Erreur:', error.response?.status || error.message);
  }

  // Test 3: Tester la connexion avec l'admin
  try {
    console.log('\n3. Test de connexion admin...');
    const loginData = {
      email: '<EMAIL>',
      password: '123456',
      rememberMe: false
    };

    const response = await axios.post('http://localhost:8000/auth/login', loginData, { timeout: 5000 });
    
    if (response.data.success) {
      console.log('✅ Connexion admin réussie');
      console.log('   Utilisateur:', response.data.data.email);
      console.log('   Rôle:', response.data.data.role);
    }
  } catch (error) {
    console.log('❌ Connexion admin échouée');
    console.log('   Erreur:', error.response?.data?.message || error.message);
  }

  return true;
}

// Exécuter le test
testBackend().then((success) => {
  if (success) {
    console.log('\n🎉 Tests terminés');
  } else {
    console.log('\n💥 Tests interrompus - serveur non accessible');
  }
}).catch(console.error);
