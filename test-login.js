// Script de test pour vérifier la fonctionnalité de connexion
const axios = require('axios');

async function testLogin() {
  console.log('🧪 Test de la fonctionnalité de connexion...\n');

  // Test 1: Vérifier que le serveur backend est accessible
  try {
    console.log('1. Test de connectivité au serveur backend...');
    const healthCheck = await axios.get('http://localhost:8000/auth');
    console.log('✅ Serveur backend accessible');
  } catch (error) {
    console.log('❌ Serveur backend non accessible:', error.message);
    console.log('   Assurez-vous que le serveur backend fonctionne sur le port 8000');
    return;
  }

  // Test 2: Tester la connexion avec des identifiants valides
  try {
    console.log('\n2. Test de connexion avec des identifiants...');
    
    // Utiliser l'utilisateur admin khalil
    const loginData = {
      email: '<EMAIL>',
      password: 'password123', // Mot de passe par défaut
      rememberMe: false
    };

    const response = await axios.post('http://localhost:8000/auth/login', loginData);
    
    if (response.data.success) {
      console.log('✅ Connexion réussie');
      console.log('   Données utilisateur:', response.data.data);
    } else {
      console.log('❌ Connexion échouée:', response.data.message);
    }
  } catch (error) {
    console.log('❌ Erreur lors de la connexion:', error.response?.data?.message || error.message);
    
    if (error.response?.status === 400) {
      console.log('   Cela peut indiquer que l\'utilisateur n\'existe pas ou que le mot de passe est incorrect');
    }
  }

  // Test 3: Vérifier la liste des utilisateurs
  try {
    console.log('\n3. Vérification des utilisateurs existants...');
    const usersResponse = await axios.get('http://localhost:8000/auth');
    
    if (usersResponse.data.success && usersResponse.data.data) {
      console.log('✅ Utilisateurs trouvés:', usersResponse.data.data.length);
      usersResponse.data.data.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.email} (${user.role})`);
      });
    } else {
      console.log('❌ Aucun utilisateur trouvé');
    }
  } catch (error) {
    console.log('❌ Erreur lors de la récupération des utilisateurs:', error.message);
  }

  console.log('\n📋 Résumé des tests terminé');
}

// Exécuter le test
testLogin().catch(console.error);
