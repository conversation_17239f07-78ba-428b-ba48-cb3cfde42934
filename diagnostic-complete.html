<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Complet - MKA LMS</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header { text-align: center; color: #1976d2; margin-bottom: 30px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #1976d2; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #1565c0; }
        .result { margin-top: 15px; padding: 10px; border-radius: 4px; display: none; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-unknown { background-color: #ffc107; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .email-template { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Diagnostic Complet - MKA LMS</h1>
            <p>Diagnostic et test de tous les composants du système de connexion</p>
        </div>

        <!-- État des serveurs -->
        <div class="test-section info">
            <h3>📊 État des serveurs</h3>
            <div id="server-status">
                <p><span id="backend-indicator" class="status-indicator status-unknown"></span>Backend (Port 8000): <span id="backend-status">Vérification...</span></p>
                <p><span id="frontend-indicator" class="status-indicator status-unknown"></span>Frontend (Port 3000): <span id="frontend-status">Vérification...</span></p>
            </div>
            <button onclick="checkServerStatus()">Vérifier l'état des serveurs</button>
        </div>

        <!-- Test de connectivité -->
        <div class="test-section info">
            <h3>🌐 Test de connectivité</h3>
            <button onclick="testBackend()">Tester Backend (Port 8000)</button>
            <button onclick="testFrontend()">Tester Frontend (Port 3000)</button>
            <button onclick="testBothServers()">Tester les deux serveurs</button>
            <div id="connectivity-result" class="result"></div>
        </div>

        <!-- Simulation email -->
        <div class="test-section info">
            <h3>📧 Simulation de l'email de bienvenue</h3>
            <div class="email-template">
                <h4 style="color:#1976d2;text-align:center">Bienvenue sur la plateforme LMS!</h4>
                <p>Votre compte a été créé avec succès. Voici vos identifiants de connexion :</p>
                <div style="background:#f5f5f5;padding:15px;border-radius:5px;margin:20px 0">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Mot de passe temporaire:</strong> <span style="font-family:monospace;background:#e0e0e0;padding:3px 6px;border-radius:3px">123456</span></p>
                    <p><strong>Rôle:</strong> Admin</p>
                </div>
                <div style="text-align:center;margin-top:30px">
                    <a href="http://localhost:3000/login" 
                       style="background:#1976d2;color:white;padding:12px 24px;text-decoration:none;border-radius:4px;display:inline-block;font-weight:bold"
                       onclick="testEmailLink(event)">
                        Se connecter à la plateforme
                    </a>
                </div>
            </div>
        </div>

        <!-- Test de connexion -->
        <div class="test-section info">
            <h3>🔐 Test de connexion</h3>
            <form onsubmit="testLogin(event)">
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">Mot de passe:</label>
                    <input type="password" id="password" value="123456" required>
                </div>
                <div class="form-group">
                    <label><input type="checkbox" id="rememberMe"> Se souvenir de moi</label>
                </div>
                <button type="submit">Tester la connexion</button>
            </form>
            <div id="login-result" class="result"></div>
        </div>

        <!-- Solutions alternatives -->
        <div class="test-section warning">
            <h3>🛠️ Solutions alternatives</h3>
            <p>Si les serveurs ne fonctionnent pas, voici des solutions alternatives :</p>
            <button onclick="showAlternativeSolutions()">Afficher les solutions</button>
            <div id="alternative-solutions" style="display: none; margin-top: 15px;">
                <h4>Option 1: Serveur backend alternatif (Port 8001)</h4>
                <button onclick="testAlternativeBackend()">Tester Backend Port 8001</button>
                
                <h4>Option 2: Mode simulation (sans serveur)</h4>
                <button onclick="simulateLogin()">Simuler la connexion</button>
                
                <h4>Option 3: Instructions manuelles</h4>
                <pre>
1. Ouvrir un terminal dans le dossier backend
2. Exécuter: node quick-server.js
3. Ouvrir un autre terminal dans le dossier frontend  
4. Exécuter: node serve-frontend.js
5. Aller sur http://localhost:3000/login
                </pre>
            </div>
        </div>

        <!-- Logs de diagnostic -->
        <div class="test-section info">
            <h3>📝 Logs de diagnostic</h3>
            <button onclick="clearLogs()">Effacer les logs</button>
            <pre id="diagnostic-logs">Logs de diagnostic apparaîtront ici...</pre>
        </div>
    </div>

    <script>
        let logs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            document.getElementById('diagnostic-logs').textContent = logs.join('\n');
        }

        function clearLogs() {
            logs = [];
            document.getElementById('diagnostic-logs').textContent = 'Logs effacés...';
        }

        async function checkServerStatus() {
            addLog('🔍 Vérification de l\'état des serveurs...');
            
            // Test backend
            try {
                const backendResponse = await fetch('http://localhost:8000/', { timeout: 3000 });
                if (backendResponse.ok) {
                    document.getElementById('backend-indicator').className = 'status-indicator status-online';
                    document.getElementById('backend-status').textContent = 'En ligne ✅';
                    addLog('✅ Backend: En ligne');
                } else {
                    throw new Error('Backend non accessible');
                }
            } catch (error) {
                document.getElementById('backend-indicator').className = 'status-indicator status-offline';
                document.getElementById('backend-status').textContent = 'Hors ligne ❌';
                addLog('❌ Backend: Hors ligne - ' + error.message);
            }

            // Test frontend
            try {
                const frontendResponse = await fetch('http://localhost:3000/', { timeout: 3000 });
                if (frontendResponse.ok) {
                    document.getElementById('frontend-indicator').className = 'status-indicator status-online';
                    document.getElementById('frontend-status').textContent = 'En ligne ✅';
                    addLog('✅ Frontend: En ligne');
                } else {
                    throw new Error('Frontend non accessible');
                }
            } catch (error) {
                document.getElementById('frontend-indicator').className = 'status-indicator status-offline';
                document.getElementById('frontend-status').textContent = 'Hors ligne ❌';
                addLog('❌ Frontend: Hors ligne - ' + error.message);
            }
        }

        async function testBackend() {
            const resultDiv = document.getElementById('connectivity-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '⏳ Test du backend...';
            addLog('🧪 Test de connectivité backend...');

            try {
                const response = await fetch('http://localhost:8000/', { timeout: 5000 });
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ Backend accessible<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                addLog('✅ Backend test réussi');
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Backend non accessible: ${error.message}`;
                addLog('❌ Backend test échoué: ' + error.message);
            }
        }

        async function testFrontend() {
            addLog('🧪 Test de connectivité frontend...');
            try {
                const response = await fetch('http://localhost:3000/', { timeout: 5000 });
                if (response.ok) {
                    addLog('✅ Frontend accessible');
                    window.open('http://localhost:3000/login', '_blank');
                } else {
                    addLog('❌ Frontend non accessible');
                }
            } catch (error) {
                addLog('❌ Frontend test échoué: ' + error.message);
            }
        }

        async function testBothServers() {
            await testBackend();
            await testFrontend();
        }

        function testEmailLink(event) {
            event.preventDefault();
            addLog('📧 Test du lien email...');
            
            // Tester d'abord si le frontend est accessible
            fetch('http://localhost:3000/', { timeout: 3000 })
                .then(response => {
                    if (response.ok) {
                        addLog('✅ Lien email fonctionnel - redirection...');
                        window.open('http://localhost:3000/login', '_blank');
                    } else {
                        addLog('❌ Frontend non accessible pour le lien email');
                        alert('❌ Le serveur frontend n\'est pas accessible sur http://localhost:3000');
                    }
                })
                .catch(error => {
                    addLog('❌ Erreur lien email: ' + error.message);
                    alert('❌ Erreur: Le serveur frontend n\'est pas démarré.\n\nVeuillez démarrer le serveur frontend sur le port 3000.');
                });
        }

        async function testLogin(event) {
            event.preventDefault();
            
            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '⏳ Test de connexion...';

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            addLog(`🔐 Tentative de connexion: ${email}`);

            try {
                const response = await fetch('http://localhost:8000/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password, rememberMe })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Connexion réussie!<br>
                        <strong>Utilisateur:</strong> ${data.data.email}<br>
                        <strong>Rôle:</strong> ${data.data.role}<br>
                        <strong>Token:</strong> ${data.data.access_token}`;
                    addLog('✅ Connexion réussie pour: ' + email);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Connexion échouée: ${data.message}`;
                    addLog('❌ Connexion échouée: ' + data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur: ${error.message}`;
                addLog('❌ Erreur de connexion: ' + error.message);
            }
        }

        function showAlternativeSolutions() {
            document.getElementById('alternative-solutions').style.display = 'block';
            addLog('📋 Solutions alternatives affichées');
        }

        async function testAlternativeBackend() {
            addLog('🧪 Test du backend alternatif (port 8001)...');
            try {
                const response = await fetch('http://localhost:8001/', { timeout: 3000 });
                if (response.ok) {
                    addLog('✅ Backend alternatif accessible');
                } else {
                    addLog('❌ Backend alternatif non accessible');
                }
            } catch (error) {
                addLog('❌ Backend alternatif non disponible: ' + error.message);
            }
        }

        function simulateLogin() {
            addLog('🎭 Simulation de connexion...');
            const resultDiv = document.getElementById('login-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `✅ Connexion simulée réussie!<br>
                <strong>Mode:</strong> Simulation<br>
                <strong>Utilisateur:</strong> <EMAIL><br>
                <strong>Rôle:</strong> Admin<br>
                <strong>Status:</strong> Connecté (simulation)`;
            addLog('✅ Connexion simulée avec succès');
        }

        // Vérification automatique au chargement
        window.onload = function() {
            addLog('🚀 Diagnostic démarré');
            setTimeout(checkServerStatus, 1000);
        };
    </script>
</body>
</html>
