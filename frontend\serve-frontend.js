// Serveur simple pour servir le frontend React
const express = require('express');
const path = require('path');
const app = express();
const PORT = 3000;

console.log('🚀 Démarrage du serveur frontend...');

// Servir les fichiers statiques du build
app.use(express.static(path.join(__dirname, 'build')));

// Servir les fichiers statiques du dossier public
app.use(express.static(path.join(__dirname, 'public')));

// Route pour toutes les autres requêtes (SPA routing)
app.get('*', (req, res) => {
  console.log('📡 Requête frontend:', req.path);
  
  // Si le fichier build/index.html existe, le servir
  const buildIndexPath = path.join(__dirname, 'build', 'index.html');
  const publicIndexPath = path.join(__dirname, 'public', 'index.html');
  
  // Essayer d'abord le build, puis le public
  const fs = require('fs');
  if (fs.existsSync(buildIndexPath)) {
    res.sendFile(buildIndexPath);
  } else if (fs.existsSync(publicIndexPath)) {
    res.sendFile(publicIndexPath);
  } else {
    // Créer une page simple si aucun index.html n'existe
    res.send(`
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>MKA LMS - Page de connexion</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
          .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
          .form-group { margin: 15px 0; }
          label { display: block; margin-bottom: 5px; font-weight: bold; }
          input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
          button { background: #1976d2; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; width: 100%; }
          button:hover { background: #1565c0; }
          .result { margin-top: 15px; padding: 10px; border-radius: 4px; display: none; }
          .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
          .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 style="text-align: center; color: #1976d2;">🎓 MKA LMS - Connexion</h1>
          <p style="text-align: center;">Bienvenue sur la plateforme d'apprentissage</p>
          
          <form onsubmit="handleLogin(event)">
            <div class="form-group">
              <label for="email">Email :</label>
              <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
              <label for="password">Mot de passe :</label>
              <input type="password" id="password" value="123456" required>
            </div>
            <div class="form-group">
              <label>
                <input type="checkbox" id="rememberMe"> Se souvenir de moi
              </label>
            </div>
            <button type="submit">Se connecter</button>
          </form>
          
          <div id="result" class="result"></div>
          
          <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h4>📋 Identifiants de test :</h4>
            <p><strong>Email :</strong> <EMAIL></p>
            <p><strong>Mot de passe :</strong> 123456</p>
          </div>
        </div>

        <script>
          async function handleLogin(event) {
            event.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '⏳ Connexion en cours...';

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            try {
              const response = await fetch('http://localhost:8000/auth/login', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  email: email,
                  password: password,
                  rememberMe: rememberMe
                })
              });

              const data = await response.json();

              if (response.ok && data.success) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ Connexion réussie ! Redirection vers la page d\\'accueil...';
                
                // Simuler la redirection vers la page d'accueil
                setTimeout(() => {
                  resultDiv.innerHTML = '🎉 Bienvenue ' + data.data.name + ' ! Vous êtes connecté en tant que ' + data.data.role;
                }, 1500);
              } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Connexion échouée : ' + (data.message || 'Erreur inconnue');
              }
            } catch (error) {
              resultDiv.className = 'result error';
              resultDiv.innerHTML = '❌ Erreur de connexion : ' + error.message;
            }
          }
        </script>
      </body>
      </html>
    `);
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🎉 Serveur frontend démarré avec succès !`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log(`🔗 Page de connexion: http://localhost:${PORT}/login`);
  console.log(`📱 Toutes les routes redirigent vers la page de connexion`);
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur frontend...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur frontend...');
  process.exit(0);
});
