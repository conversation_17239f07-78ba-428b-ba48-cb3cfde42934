import { PrismaService } from 'nestjs-prisma';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { MailService } from '../mail/mail.service';
export declare class UsersService {
    private readonly prisma;
    private readonly mailService;
    constructor(prisma: PrismaService, mailService: MailService);
    private hashPassword;
    private generateTempPassword;
    create(createUserDto: CreateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    } | null>;
    update(id: number, updateUserDto: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    remove(id: number): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        password: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
        createdAt: Date;
        updatedAt: Date;
        resetToken: string | null;
        resetTokenExpiry: Date | null;
    }>;
    findById(id: number): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    } | null>;
    findByEmail(email: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    } | null>;
    updateByEmail(email: string, updateUserDto: UpdateUserDto): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
    updateProfilePic(id: number, profilePicPath: string): Promise<{
        id: number;
        role: import(".prisma/client").$Enums.Role;
        email: string;
        name: string | null;
        phone: string | null;
        profilePic: string | null;
        location: string | null;
        skills: string[];
        about: string | null;
    }>;
}
